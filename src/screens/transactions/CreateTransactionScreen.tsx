import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, ScrollView, TouchableOpacity } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import uuid from 'react-native-uuid';
import { RootState } from '../../store/store';
import { addTransaction, updateTransaction } from '../../store/slices/transactionSlice';
import { TransactionType, TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { canCreateTransaction } from '../../utils/permissions';
import TransactionForm from '../../components/forms/TransactionForm';

export default function CreateTransactionScreen() {
  const navigation = useNavigation();
  const route = useRoute<any>();
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<TransactionType>(
    route.params?.type || TransactionType.CASH_SPENT
  );

  if (!user) return null;

  const transactionTypes = [
    {
      type: TransactionType.CASH_SPENT,
      title: 'Record Expense',
      description: 'Record money you have spent',
      icon: '💳',
      available: canCreateTransaction(user.role, TransactionType.CASH_SPENT),
    },
    {
      type: TransactionType.CASH_GIVEN,
      title: 'Give Cash',
      description: 'Give cash to a student',
      icon: '💰',
      available: canCreateTransaction(user.role, TransactionType.CASH_GIVEN),
    },
    {
      type: TransactionType.CASH_RETURNED,
      title: 'Return Cash',
      description: 'Return unused cash',
      icon: '↩️',
      available: canCreateTransaction(user.role, TransactionType.CASH_RETURNED),
    },
  ];

  const availableTypes = transactionTypes.filter(t => t.available);

  const handleSubmit = async (data: any) => {
    setLoading(true);
    try {
      const transaction = {
        id: uuid.v4() as string,
        type: selectedType,
        status: TransactionStatus.PENDING,
        amount: parseFloat(data.amount),
        currency: 'USD',
        description: data.description,
        purpose: data.purpose,
        glCode: data.glCode,
        programCode: data.programCode,
        receiptUri: data.receiptUri,
        createdBy: user.id,
        recipient: data.recipient,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      dispatch(addTransaction(transaction));
      
      Alert.alert(
        'Success',
        'Transaction submitted successfully',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create transaction');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async (data: any) => {
    setLoading(true);
    try {
      const transaction = {
        id: uuid.v4() as string,
        type: selectedType,
        status: TransactionStatus.DRAFT,
        amount: parseFloat(data.amount),
        currency: 'USD',
        description: data.description,
        purpose: data.purpose,
        glCode: data.glCode,
        programCode: data.programCode,
        receiptUri: data.receiptUri,
        createdBy: user.id,
        recipient: data.recipient,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      dispatch(addTransaction(transaction));
      
      Alert.alert(
        'Draft Saved',
        'Transaction saved as draft',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save draft');
    } finally {
      setLoading(false);
    }
  };

  const getFormTitle = () => {
    switch (selectedType) {
      case TransactionType.CASH_SPENT:
        return 'Log Cash Spent';
      case TransactionType.CASH_GIVEN:
        return 'Log Cash Given';
      case TransactionType.CASH_RETURNED:
        return 'Log Cash Returned';
      default:
        return 'New Transaction';
    }
  };

  if (availableTypes.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>No Access</Text>
          <Text style={styles.errorMessage}>
            You don't have permission to create transactions.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>{getFormTitle()}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Transaction Type Selector */}
      {availableTypes.length > 1 && (
        <View style={styles.typeSelector}>
          <Text style={styles.typeSelectorTitle}>Transaction Type</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.typeOptions}>
              {availableTypes.map((type) => (
                <TouchableOpacity
                  key={type.type}
                  style={[
                    styles.typeOption,
                    selectedType === type.type && styles.typeOptionActive,
                  ]}
                  onPress={() => setSelectedType(type.type)}
                >
                  <Text style={styles.typeIcon}>{type.icon}</Text>
                  <Text style={[
                    styles.typeTitle,
                    selectedType === type.type && styles.typeTextActive,
                  ]}>
                    {type.title}
                  </Text>
                  <Text style={[
                    styles.typeDescription,
                    selectedType === type.type && styles.typeTextActive,
                  ]}>
                    {type.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      )}

      {/* Transaction Form */}
      <TransactionForm
        type={selectedType}
        onSubmit={handleSubmit}
        onSaveDraft={handleSaveDraft}
        loading={loading}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.background,
  },
  backButton: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  placeholder: {
    width: 50,
  },
  typeSelector: {
    backgroundColor: Colors.background,
    paddingVertical: 16,
  },
  typeSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  typeOptions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  typeOption: {
    width: 140,
    padding: 16,
    borderRadius: 12,
    backgroundColor: Colors.gray100,
    alignItems: 'center',
  },
  typeOptionActive: {
    backgroundColor: Colors.primary,
  },
  typeIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  typeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  typeTextActive: {
    color: Colors.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  errorMessage: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
